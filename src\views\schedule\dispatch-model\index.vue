<template>
  <div class="common-table-page" style="display: flex; flex-direction: column; background: #ffffff; padding: 24px">
    <!-- 第一大区块 - 顶部 -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
      <div>
        <a-select v-model="selectedScheme" style="width: 350px;" placeholder="请选择调度方案">
          <a-select-option v-for="item in schemeOptions" :key="item.value" :value="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </div>
      <div>
        <a-button type="primary" style="color: #fff; background: #165DFF; font-size: 14px; font-weight: 400;"
          @click="goToSchemeManage">
          方案管理
        </a-button>
      </div>
    </div>

    <!-- 第二大区块 - 中间标题和tab -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
      <div>
        <h3 style="margin: 0; font-size: 20px; color: #1D2129; font-weight: 600;">水库调度模型</h3>
      </div>
      <div>
        <a-radio-group v-model="activeTab" button-style="solid">
          <a-radio-button value="overview">方案概览</a-radio-button>
          <a-radio-button value="detail">方案详情</a-radio-button>
        </a-radio-group>
      </div>
    </div>

    <!-- 第三大区块 - 底部内容 -->
    <div style="display: flex; height: calc(100vh - 200px);">
      <!-- 左侧信息展示 - 占30% -->
      <div class="info-panel" style="width: 30%; margin-right: 16px; border-radius: 8px; overflow: hidden;">
        <!-- 来水方案名称区块 -->
        <div class="water-scheme">
          <h4 class="block-title">来水方案名称</h4>
          <div class="scheme-info">
            <div class="info-item full-width">
              <span class="label">来水方案名称:</span>
              <span class="value">{{ dataSource?.schemeName || '自动预报-桃花江灌区-2025051917~2025052216' }}</span>
            </div>
            <div class="info-item full-width">
              <span class="label">预报时段:</span>
              <span class="value">{{ dataSource?.forecastPeriod || '2025-05-21 16:00 ~ 2025-05-24 16:00' }}</span>
            </div>
            <div class="info-cards">
              <div v-for="card in waterSchemeCards" :key="card.key" class="info-card"
                :style="{ background: card.background }">
                <div class="card-header">
                  <i class="icon" :class="card.iconClass"></i>
                  <span class="card-title">{{ card.title }}</span>
                </div>
                <div class="card-content">
                  <span class="card-value">{{ dataSource?.[card.dataKey] || card.defaultValue }}</span>
                  <span class="card-unit">{{ card.unit }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 出库信息区块（仅概览tab显示） -->
        <div class="outflow-info">
          <h4 class="block-title">出库信息</h4>
          <div v-for="(cardGroup, groupIndex) in outflowInfoCardGroups" :key="groupIndex"
            class="info-cards info-cards-bottom">
            <div v-for="card in cardGroup" :key="card.key" class="info-card" :style="{
              background: card.background,
              visibility: card.hidden ? 'hidden' : 'visible'
            }">
              <div class="card-header">
                <i class="icon" :class="card.iconClass"></i>
                <span class="card-title">{{ card.title }}</span>
              </div>
              <div class="card-content">
                <span class="card-value">{{ card.hidden ? '' : (dataSource?.[card.dataKey] || card.defaultValue)
                  }}</span>
                <span class="card-unit">{{ card.hidden ? '' : card.unit }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 右侧内容区块 -->
      <div style="width: 70%; display: flex; flex-direction: column;">
        <!-- 概览tab -->
        <div v-if="activeTab === 'overview'" class="reservoir-info">
          <h4 style="margin: 0 0 16px 0; font-size: 16px; color: #1D2129; font-weight: 600;">水库特征信息</h4>
          <div class="reservoir-chart">
            <!-- 水库特征水位示意图 -->
            <div class="reservoir-diagram">
              <!-- basic图片 -->
              <img src="@/assets/images/base.png" class="basic-img" />
              <!-- 四个特征图片 -->
              <img src="@/assets/images/drinking-outlets.png" class="feature-img drinking-outlets" />
              <div class="feature-label drinking-outlets-label">引水口</div>
              <img src="@/assets/images/overflow-weir-crest.png" class="feature-img overflow-weir-crest" />
              <div class="feature-label overflow-weir-label">溢流堰顶</div>
              <img src="@/assets/images/sluice-gate.png" class="feature-img sluice-gate" />
              <div class="feature-label sluice-gate-label">闸门</div>
              <img src="@/assets/images/top-sluice.png" class="feature-img top-sluice" />
              <!-- 水的示意图 -->
              <div class="water-animation">
                <img src="@/assets/images/water-right.png" class="water-img" />
                <!-- 水面流动效果层 -->
                <!-- <div class="water-surface-effect">
                              <div class="wave wave-1"></div>
                              <div class="wave wave-2"></div>
                              <div class="wave wave-3"></div>
                              <div class="water-shimmer"></div>
                            </div> -->
              </div>
              <div class="water-animation-left">
                <img src="@/assets/images/water-left.png" class="water-img" />
                <!-- 水面流动效果层 -->
                <div class="water-surface-effect">
                  <div class="wave wave-1"></div>
                  <div class="wave wave-2"></div>
                  <div class="wave wave-3"></div>
                  <div class="water-shimmer"></div>
                </div>
              </div>
              <!-- 固定水位线 -->
              <div v-for="item in fixedLevels" :key="item.label" class="level-line" :style="{ top: item.top }">
                <span class="level-label" :class="{ 'top-label': item.label === '校验洪水位' }">{{ item.label }} <span
                    class="level-value">{{ item.value }}</span> m</span>
              </div>
              <!-- 调度方案水位线 -->
              <div v-for="item in adjustedSchemeLevels" :key="item.label" class="scheme-line"
                :style="{
                  top: item.top,
                  borderTopColor: item.color,
                  left: item.styleLeft || '18%',
                  width: item.styleWidth || '46%'
                }">
                <span class="scheme-label" :style="{ color: item.color }">{{ item.label }} {{ item.value }} m</span>
              </div>
              <!-- 垂直指示线 -->
              <div v-for="(line, index) in indicatorLines" :key="index" class="indicator-line"
                :class="{ 'right-line': line.position.right }" :style="{
                  ...line.position,
                  height: line.height
                }">
                <img src="@/assets/images/arrow-top.png" class="arrow-img top-arrow" />
                <img src="@/assets/images/arrow-top.png" class="arrow-img bottom-arrow flip-vertical" />
                <span class="indicator-label" :class="`${line.labelPosition}-label`">{{ line.label.split(' ')[0] }} <span
                    class="indicator-value">{{ line.label.split(' ')[1] }}</span> {{ line.label.split(' ')[2] ||
                  ''}}</span>
              </div>
            </div>
          </div>
        </div>
        <!-- 详情tab -->
        <div v-if="activeTab === 'detail'" style="height: 100%; display: flex; flex-direction: column;">
          <div style="height: 50%;">
            <BarAndLineMixChart :dataSource="chartData" />
          </div>
          <ResultTable :dataSource="resultData?.resvrDispResList || []" :resultData="resultData" style="height: 50%;" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getOptions, getValueByKey } from '@/api/common'
import { getResvrDispPage, getDispRes } from './services.js'
import moment from 'moment'
import BarAndLineMixChart from './modules/AddModal/BarAndLineMixChart.vue'
import ResultTable from './modules/AddModal/ResultTable.vue'

// 水位区间常量
const MIN_LEVEL = 112.60;
const MAX_LEVEL = 140.94;
const CONTAINER_HEIGHT = 400;
const PX_PER_METER = CONTAINER_HEIGHT / (MAX_LEVEL - MIN_LEVEL);

function getTop(level) {
  return CONTAINER_HEIGHT - (level - MIN_LEVEL) * PX_PER_METER;
}

// 根据特征图片位置计算固定水位线位置（使用百分比）
function getFixedLevelsPosition() {
  return [
    { label: '死水位', value: 112.60, top: '50%' }, 
    { label: '灌溉最低水位', value: 115.00, top: '44%' }, 
    { label: '汛期限制水位', value: 138.60, top: '28%' }, 
    { label: '防洪限制水位', value: 139.60, top: '17%' }, 
    { label: '正常水位', value: 140.60, top: '12%' }, 
    { label: '防汛设计水位', value: 140.74, top: '7%' },
    { label: '校验洪水位', value: 140.94, top: '0%' }
  ];
}

// 根据水位值计算对应的位置（支持百分比）
function getSchemeLevelPosition(level) {
  const fixedLevels = getFixedLevelsPosition();

  // 将百分比字符串转换为数值进行计算（处理calc表达式）
  const parsePosition = (pos) => {
    if (typeof pos === 'string') {
      if (pos.includes('calc(')) {
        // 处理 calc() 表达式，例如 calc(33% - 27px)
        const match = pos.match(/calc\((\d+(?:\.\d+)?)% - (\d+)px\)/);
        if (match) {
          const percentage = parseFloat(match[1]);
          const pixels = parseFloat(match[2]);
          // 简化处理：假设容器高度为400px，将px转换为百分比
          const pixelPercentage = (pixels / 400) * 100;
          return percentage - pixelPercentage;
        }
      } else if (pos.includes('%')) {
        return parseFloat(pos);
      }
    }
    return parseFloat(pos) || 0;
  };

  // 按水位值从低到高排序固定水位线
  const sortedFixedLevels = [...fixedLevels].sort((a, b) => a.value - b.value);

  // 找到水位值所在的区间
  let lowerLevel = null;
  let upperLevel = null;

  for (let i = 0; i < sortedFixedLevels.length - 1; i++) {
    if (level >= sortedFixedLevels[i].value && level <= sortedFixedLevels[i + 1].value) {
      lowerLevel = sortedFixedLevels[i];
      upperLevel = sortedFixedLevels[i + 1];
      break;
    }
  }

  // 如果水位值超出范围，使用边界值
  if (!lowerLevel || !upperLevel) {
    if (level <= sortedFixedLevels[0].value) {
      // 水位值低于最低固定水位，使用最低固定水位的位置
      return sortedFixedLevels[0].top;
    } else if (level >= sortedFixedLevels[sortedFixedLevels.length - 1].value) {
      // 水位值高于最高固定水位，使用最高固定水位的位置
      return sortedFixedLevels[sortedFixedLevels.length - 1].top;
    }
  }

  // 线性插值计算位置
  const lowerPos = parsePosition(lowerLevel.top);
  const upperPos = parsePosition(upperLevel.top);
  const lowerValue = lowerLevel.value;
  const upperValue = upperLevel.value;

  // 计算插值比例
  const ratio = (level - lowerValue) / (upperValue - lowerValue);

  // 注意：在视觉上，水位值越高，top值应该越小（越靠近顶部）
  // 所以这里是 lowerPos - ratio * (lowerPos - upperPos)
  const resultPos = lowerPos - ratio * (lowerPos - upperPos);

  return `${Math.max(0, Math.min(100, resultPos)).toFixed(2)}%`;
}

// 处理方案水位线的位置计算，包括区间判断和同区间多条线的处理
function processSchemeWaterLevels(schemeLevels) {
  const fixedLevels = getFixedLevelsPosition();

  // 将百分比字符串转换为数值进行计算
  const parsePosition = (pos) => {
    if (typeof pos === 'string' && pos.includes('%')) {
      return parseFloat(pos);
    }
    return parseFloat(pos) || 0;
  };

  // 按水位值从低到高排序固定水位线
  const sortedFixedLevels = [...fixedLevels].sort((a, b) => a.value - b.value);

  // 为每条方案水位线找到所在区间并计算基础位置
  const processedLevels = schemeLevels.map(schemeLevel => {
    const level = parseFloat(schemeLevel.value);

    // 找到水位值所在的区间
    let lowerLevel = null;
    let upperLevel = null;
    let intervalIndex = -1;

    for (let i = 0; i < sortedFixedLevels.length - 1; i++) {
      if (level >= sortedFixedLevels[i].value && level <= sortedFixedLevels[i + 1].value) {
        lowerLevel = sortedFixedLevels[i];
        upperLevel = sortedFixedLevels[i + 1];
        intervalIndex = i;
        break;
      }
    }

    // 如果水位值超出范围，使用边界值
    if (!lowerLevel || !upperLevel) {
      if (level <= sortedFixedLevels[0].value) {
        lowerLevel = sortedFixedLevels[0];
        upperLevel = sortedFixedLevels[1];
        intervalIndex = -1; // 表示低于最低区间
      } else if (level >= sortedFixedLevels[sortedFixedLevels.length - 1].value) {
        lowerLevel = sortedFixedLevels[sortedFixedLevels.length - 2];
        upperLevel = sortedFixedLevels[sortedFixedLevels.length - 1];
        intervalIndex = sortedFixedLevels.length; // 表示高于最高区间
      }
    }

    // 线性插值计算基础位置
    const lowerPos = parsePosition(lowerLevel.top);
    const upperPos = parsePosition(upperLevel.top);
    const lowerValue = lowerLevel.value;
    const upperValue = upperLevel.value;

    // 计算插值比例
    const ratio = (level - lowerValue) / (upperValue - lowerValue);
    const basePos = lowerPos - ratio * (lowerPos - upperPos);

    return {
      ...schemeLevel,
      level: level,
      intervalIndex: intervalIndex,
      basePosition: basePos,
      lowerLevel: lowerLevel,
      upperLevel: upperLevel
    };
  });

  // 按区间分组
  const intervalGroups = {};
  processedLevels.forEach(level => {
    const key = level.intervalIndex;
    if (!intervalGroups[key]) {
      intervalGroups[key] = [];
    }
    intervalGroups[key].push(level);
  });

  // 调试信息：显示区间分组情况
  console.log('方案水位线区间分组:', intervalGroups);

  // 处理每个区间内的多条水位线
  const finalLevels = [];
  Object.keys(intervalGroups).forEach(intervalKey => {
    const group = intervalGroups[intervalKey];

    if (group.length === 1) {
      // 单条线，直接使用基础位置和默认样式
      const level = group[0];
      finalLevels.push({
        ...level,
        top: `${Math.max(0, Math.min(100, level.basePosition)).toFixed(2)}%`,
        // 使用默认样式
        styleLeft: '18%',
        styleWidth: '46%'
      });
    } else {
      // 多条线，需要调整位置避免重叠
      // 按水位值从高到低排序，确保value大的在上面（top值小）
      group.sort((a, b) => b.level - a.level);

      // 计算区间的可用空间（上下固定水位线之间的百分比差）
      const upperPos = parsePosition(group[0].upperLevel.top);
      const lowerPos = parsePosition(group[0].lowerLevel.top);
      const availableSpace = lowerPos - upperPos; // 可用的百分比空间

      // 计算最小间距，确保所有线都能在区间内显示
      const minSpacing = 1.5; // 最小间距1.5%
      const requiredSpace = (group.length - 1) * minSpacing;

      // 如果所需空间超过可用空间，调整间距
      const actualSpacing = requiredSpace <= availableSpace ? minSpacing : availableSpace / (group.length - 1);

      // 调试信息：显示多条线处理过程
      console.log(`区间 ${intervalKey} (${group[0].lowerLevel.label}-${group[0].upperLevel.label}) 有 ${group.length} 条水位线:`,
        group.map(g => `${g.label}(${g.level}m)`).join(', '));
      console.log(`可用空间: ${availableSpace.toFixed(2)}%, 实际间距: ${actualSpacing.toFixed(2)}%`);
      console.log(`区间范围: ${upperPos.toFixed(2)}% - ${lowerPos.toFixed(2)}%`);

      group.forEach((level, index) => {
        // 根据在组内的位置计算调整后的位置
        // value最大的（index=0）在最上面（upperPos），依次向下排列
        const adjustedPos = upperPos + (actualSpacing * index);

        // 确保不超出区间范围
        const finalPos = Math.max(upperPos, Math.min(lowerPos, adjustedPos));

        // 计算动态样式：为重叠的水位线分配不同的left和width
        // 基础样式：left: 18%, width: 46%，总和为64%
        const baseLeft = 18;
        const baseWidth = 46;
        const totalWidth = 64; // left + width = 64%

        // 从上到下，width从大到小排列
        // index=0（最上面，value最大）width最大，index越大width越小
        const widthDecrement = Math.min(4, Math.floor(baseWidth / group.length)); // 每条线width递减量，最大4%
        const dynamicWidth = Math.max(20, baseWidth - (index * widthDecrement)); // 最小width为20%
        const dynamicLeft = totalWidth - dynamicWidth; // 保持left + width = 64%

        console.log(`${level.label}(${level.level}m): 原位置 ${level.basePosition.toFixed(2)}% -> 调整后位置 ${finalPos.toFixed(2)}%, 样式 left:${dynamicLeft}% width:${dynamicWidth}%`);

        finalLevels.push({
          ...level,
          top: `${Math.max(0, Math.min(100, finalPos)).toFixed(2)}%`,
          // 添加动态样式信息
          styleLeft: `${dynamicLeft}%`,
          styleWidth: `${dynamicWidth}%`
        });
      });
    }
  });

  // 按原始顺序返回结果
  return schemeLevels.map(original => {
    const processed = finalLevels.find(p =>
      p.label === original.label && p.value === original.value && p.color === original.color
    );
    return processed || { ...original, top: getSchemeLevelPosition(original.value) };
  });
}

export default {
  name: 'DispatchModel',
  components: { BarAndLineMixChart, ResultTable },

  data() {
    return {
      selectedScheme: undefined,
      schemeOptions: [],
      activeTab: 'overview',
      dataSource: null,
      // 来水方案名称区块的信息卡片配置
      waterSchemeCards: [
        {
          key: 'rain',
          title: '累计降雨量',
          iconClass: 'rain-icon',
          background: '#E6F5FA',
          dataKey: 'rainSum',
          defaultValue: '78.2',
          unit: 'mm'
        },
        {
          key: 'inWater',
          title: '累计来水量',
          iconClass: 'water-icon',
          background: '#E8EAFF',
          dataKey: 'inWaterSum',
          defaultValue: '1245.6',
          unit: '万m³'
        }
      ],
      // 出库信息区块的信息卡片配置（按组分组）
      outflowInfoCardGroups: [
        // [
        //   {
        //     key: 'supply',
        //     title: '供水流量',
        //     iconClass: 'supply-icon',
        //     background: '#E8F3FF',
        //     dataKey: 'supplyFlow',
        //     defaultValue: '35.6',
        //     unit: 'm³/s'
        //   },
        //   {
        //     key: 'discharge',
        //     title: '泄洪流量',
        //     iconClass: 'discharge-icon',
        //     background: '#E2F6F3',
        //     dataKey: 'dischargeFlow',
        //     defaultValue: '58.9',
        //     unit: 'm³/s'
        //   }
        // ],
        [
          {
            key: 'totalSupply',
            title: '累计供水量',
            iconClass: 'total-supply-icon',
            background: '#E8EAFF',
            dataKey: 'totalSupply',
            defaultValue: '1025.8',
            unit: '万m³'
          },
          {
            key: 'outflow',
            title: '累计泄洪量',
            iconClass: 'outflow-icon',
            background: '#FFF0E8',
            dataKey: 'outflowWater',
            defaultValue: '856.3',
            unit: '万m³'
          }
        ],
        [
          {
            key: 'supplyRate',
            title: '累计出库水量',
            iconClass: 'supply-rate-icon',
            background: '#E6F5FA',
            dataKey: 'supplyFlowRate',
            defaultValue: '42.3',
            unit: '万m³'
          },
          {
            key: 'placeholder',
            title: '',
            iconClass: '',
            background: '#E6F5FA',
            dataKey: '',
            defaultValue: '',
            unit: '',
            hidden: true
          }
        ]
      ],
      // 固定水位线
      fixedLevels: getFixedLevelsPosition(),
      // 调度方案水位线（测试数据）
      // 测试同一区间多条水位线的情况：
      // 正常水位(140.60) - 防汛设计水位(140.74)区间内有3条线：140.65, 140.68, 140.70
      // 防汛设计水位(140.74) - 校验洪水位(140.94)区间内有3条线：140.80, 140.85, 140.90
      schemeLevels: processSchemeWaterLevels([
        { label: '最低水位', value: 138.80, color: '#FF7D00' },
        { label: '起调水位', value: 140.25, color: '#00B42A' },
        { label: '末期水位', value: 139.40, color: '#14C9C9' },
        { label: '方案水位A', value: 140.65, color: '#9254DE' }, // 正常水位-防汛设计水位区间
        { label: '方案水位B', value: 140.68, color: '#722ED1' }, // 正常水位-防汛设计水位区间
        { label: '方案水位C', value: 140.70, color: '#531DAB' }, // 正常水位-防汛设计水位区间
        { label: '最高水位', value: 140.80, color: '#F53F3F' },   // 防汛设计水位-校验洪水位区间
        { label: '方案水位D', value: 140.85, color: '#EB2F96' }, // 防汛设计水位-校验洪水位区间
        { label: '方案水位E', value: 140.90, color: '#C41D7F' }  // 防汛设计水位-校验洪水位区间
      ]),
      // 处理后的调度方案水位线
      adjustedSchemeLevels: [],
      // 垂直指示线配置（使用百分比）
      indicatorLines: [
        { position: { right: '2%' }, height: '100%', label: '总库容 704 万m³', labelPosition: 'left' },
        { position: { left: '68%', bottom: '0' }, height: '50%', label: '死库容 145.57 万m³', labelPosition: 'right' },
        { position: { left: '68%', bottom: '50%' }, height: '36%', label: '兴利库容 704 万m³', labelPosition: 'right' },
        { position: { left: '72%', top: '0' }, height: '29%', label: '调洪库容 530 万m³', labelPosition: 'right' },
        { position: { left: '76%', top: '17.5%' }, height: '11%', label: '防洪库容 330 万m³', labelPosition: 'right' },
        { position: { left: '84%', bottom: '0' }, height: '84%', label: '起调库容 704 万m³', labelPosition: 'right' }
      ],
      // 详情tab相关数据
      resultData: null,
      chartData: [],
    }
  },
  computed: {},
  created() {
    this.loadSchemeOptions();
  },
  watch: {
    activeTab(newVal) {
      if (newVal === 'detail') {
        this.loadDetailData();
      }
    },
    selectedScheme(newVal) {
      if (newVal) {
        this.loadSchemeDetail();
      }
    },
  },
  methods: {
    // 加载方案选项
    loadSchemeOptions() {
      getResvrDispPage({ pageNum: 1, pageSize: 5 }).then(response => {
        const data = response.data?.data || [];
        this.schemeOptions = data.map(item => ({
          label: item.caseName || `${item.caseCode}`,
          value: item.resvrDispId
        }));

        // 默认选中第一条
        if (this.schemeOptions.length > 0) {
          this.selectedScheme = this.schemeOptions[0].value;
          this.loadSchemeDetail();
        }
      }).catch(error => {
        console.error('加载方案选项失败:', error);
        this.$message.error('加载方案选项失败');
      });
    },

    // 加载方案详情
    loadSchemeDetail() {
      if (!this.selectedScheme) return;

      getDispRes({ resvrDispId: this.selectedScheme }).then(response => {
        const data = response.data;
        if (data) {
          this.updateDataSource(data);
          this.updateResultData(data);
          this.updateChartData(data);
        }
      }).catch(error => {
        console.error('加载方案详情失败:', error);
        this.$message.error('加载方案详情失败');
      });
    },

    // 更新数据源
    updateDataSource(data) {
      // 从来水信息中获取数据
      const inWaterInfo = data.inWater || {};
      console.log("数据源",data,inWaterInfo)
      this.dataSource = {
        schemeName: data.caseName || inWaterInfo.caseName || '未知方案',
        forecastPeriod: `${data.startTime || inWaterInfo.startTime} ~ ${data.endTime}`,
        rainSum: inWaterInfo.sumRain?.toFixed(1) || data.sumRainfall?.toFixed(1) || '0.0',
        inWaterSum: inWaterInfo.sumInWater?.toFixed(1) || data.sumInWater?.toFixed(1) || '0.0',
        supplyFlow: '0.0', // 根据实际数据结构调整
        outflowWater: data.sumFloodWater?.toFixed(1) || '0.0',
        totalSupply: data.sumOutWater?.toFixed(1) || '0.0',
        dischargeFlow: '0.0', // 根据实际数据结构调整
        supplyFlowRate: data.sumOutWater?.toFixed(1) || '0.0',
        // 方案水位数据
        minLevel: data.minWaterLevel || '0.0',
        startLevel: data.startWaterLevel || '0.0',
        endLevel: data.endWaterLevel || '0.0',
        maxLevel: data.maxWaterLevel || '0.0'
      };

      // 更新方案水位线
      const rawSchemeLevels = [
        { label: '最低水位', value: this.dataSource.minLevel, color: '#FF7D00' },
        { label: '起调水位', value: this.dataSource.startLevel, color: '#00B42A' },
        { label: '末期水位', value: this.dataSource.endLevel, color: '#14C9C9' },
        { label: '最高水位', value: this.dataSource.maxLevel, color: '#F53F3F' }
      ];

      // 使用新的处理函数来计算方案水位线位置，包括区间判断和同区间多条线的处理
      this.adjustedSchemeLevels = processSchemeWaterLevels(rawSchemeLevels);
    },

    // 更新结果数据
    updateResultData(data) {
      this.resultData = {
        caseCode: data.caseCode,
        caseName: data.caseName,
        startTime: data.startTime,
        endTime: data.endTime,
        startWaterLevel: data.startWaterLevel?.toFixed(2) || '0.00',
        endWaterLevel: data.endWaterLevel?.toFixed(2) || '0.00',
        totalRainfall: data.sumRainfall?.toFixed(1) || '0.0',
        resvrDispResList: data.resvrDispResList || [],
      };
    },

    // 更新图表数据
    updateChartData(data) {
      const resvrDispResList = data.resvrDispResList || [];
      if (resvrDispResList.length === 0) {
        this.chartData = [];
        return;
      }

      // 构建图表数据
      const rainData = {
        name: '时段雨量',
        data: resvrDispResList.map(el => [el.tm, parseFloat(el.rain || 0)]),
      };

      const sumRainData = {
        name: '累计降雨量',
        data: rainData.data.map((el, idx) => {
          const sum = rainData.data.slice(0, idx + 1).reduce((a, b) => a + b[1], 0);
          return [el[0], +sum.toFixed(1)];
        }),
      };

      this.chartData = [
        rainData,
        sumRainData,
        {
          name: '水位',
          data: resvrDispResList.map(el => [el.tm, parseFloat(el.wlv || 0)]),
        },
        {
          name: '入库流量',
          data: resvrDispResList.map(el => [el.tm, parseFloat(el.inflow || 0)]),
        },
        {
          name: '供水流量',
          data: resvrDispResList.map(el => [el.tm, parseFloat(el.outflow || 0)]),
        },
        {
          name: '泄洪流量',
          data: resvrDispResList.map(el => [el.tm, parseFloat(el.floodflow || 0)]),
        },
      ];
    },


    // 处理重叠的水位线（支持百分比）
    adjustOverlappingLines(lines) {
      const adjustedLines = [...lines];
      const threshold = 3; // 3%内认为是重叠

      // 将百分比字符串转换为数值进行比较
      const parsePosition = (pos) => {
        if (typeof pos === 'string' && pos.includes('%')) {
          return parseFloat(pos);
        }
        return pos;
      };

      // 按top值排序
      adjustedLines.sort((a, b) => parsePosition(a.top) - parsePosition(b.top));

      for (let i = 1; i < adjustedLines.length; i++) {
        const current = adjustedLines[i];
        const previous = adjustedLines[i - 1];

        const currentPos = parsePosition(current.top);
        const previousPos = parsePosition(previous.top);

        // 如果两条线太接近，调整当前线的位置
        if (Math.abs(currentPos - previousPos) < threshold) {
          // 如果是相同数值，一个上移一个下移
          if (current.value === previous.value) {
            previous.top = `${(previousPos - 1.5).toFixed(2)}%`;
            current.top = `${(currentPos + 1.5).toFixed(2)}%`;
          } else {
            // 否则当前线下移
            current.top = `${(previousPos + threshold).toFixed(2)}%`;
          }
        }
      }

      return adjustedLines;
    },
    goToSchemeManage() {
      this.$router.push('/schedule/dispatch-model-list')
    },
    loadDetailData() {
      // 详情tab的数据已经在loadSchemeDetail中加载了
      // 这里不需要额外处理，数据已经通过updateResultData和updateChartData更新
    },
  },
}
</script>

<style lang="less" scoped>
.info-panel {
  background-image: linear-gradient(to bottom, rgba(105, 187, 255, 0.15), rgba(64, 196, 249, 0));
  display: flex;
  flex-direction: column;
}

.block-title {
  font-size: 16px;
  color: #1D2129;
  font-weight: 600;
  margin: 16px 0;
  padding: 0 16px;
}



.scheme-info {
  padding: 0 16px;
}

.info-item {
  margin-bottom: 12px;
  display: flex;
  // align-items: center;
  flex-direction: column;

  &.full-width {
    width: 100%;
  }

  .label {
    color: #4E5969;
    font-size: 14px;
    margin-right: 8px;
  }

  .value {
    color: #1D2129;
    font-size: 14px;
    font-weight: 500;
  }
}

.info-cards {
  display: flex;
  justify-content: space-between;
  // margin-bottom: 16px;
}

.info-cards-bottom {
  padding: 0 16px;
  margin-bottom: 16px;
}

.info-card {
  width: 48%;
  border-radius: 8px;
  padding: 16px;

  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .icon {
      width: 24px;
      height: 24px;
      margin-right: 8px;
      display: inline-block;
    }

    .card-title {
      color: #4E5969;
      font-size: 14px;
    }
  }

  .card-content {
    .card-value {
      font-size: 20px;
      font-weight: 700;
      color: #1D2129;
    }

    .card-unit {
      font-size: 14px;
      color: #4E5969;
      margin-left: 4px;
    }
  }
}

.reservoir-info {
  height: 100%;
  padding: 16px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

// 图标样式，实际项目中应该使用真实的图标
.rain-icon {
  background: url('@/assets/images/forecast-cumulative-precipitation.png') 0 0 no-repeat;
  background-size: 100%;
}

.water-icon {
  background: url('@/assets/images/cumulative-water-intake.png') 0 0 no-repeat;
  background-size: 100%;
}

.supply-icon {
  background: url('@/assets/images/water-supply-flow.png') 0 0 no-repeat;
  background-size: 100%;
}

.outflow-icon {
  background: url('@/assets/images/flood-flow.png') 0 0 no-repeat;
  background-size: 100%;
}

.total-supply-icon {
  background: url('@/assets/images/cumulative-water-supply.png') 0 0 no-repeat;
  background-size: 100%;
}

.discharge-icon {
  background: url('@/assets/images/forecast-out-water.png') 0 0 no-repeat;
  background-size: 100%;
}

.supply-rate-icon {
  background: url('@/assets/images/cumulative-flood-discharge.png') 0 0 no-repeat;
  background-size: 100%;
}

@font-face {
  font-family: 'AlimamaDaoLiTi';
  src: url('@/assets/font/AlimamaDaoLiTi.ttf');
}

.reservoir-chart {
  height: calc(100% - 50px);
  background: #f5f7fa;
  border-radius: 8px;
  position: relative;
  overflow: visible;
  /* 修改为visible以显示溢出内容 */
}

.reservoir-diagram {
  position: relative;
  width: 100%;
  height: 100%;
}

.basic-img {
  position: absolute;
  left: 0;
  bottom: 0;
  height: 100%;
  width: 65%;
  z-index: 7;
}

.feature-img {
  position: absolute;
  left: 63%;
  z-index: 2;
}

.drinking-outlets {
  bottom: 0;
  height: 50%;
}

.overflow-weir-crest {
  bottom: 50%;
  left: 64%;
  height: 16.67%;
}

.sluice-gate {
  bottom: 67%;
  left: 64%;
  height: 15.67%;
}

.top-sluice {
  bottom: 83%;
  left: 64%;
  height: 16.87%;
}

.level-line {
  position: absolute;
  left: 39%;
  /* 从图片左侧开始 */
  width: 25%;
  /* 只延伸260px */
  height: 1px;
  background: #86909C;
  z-index: 10;

  &::before {
    content: "";
    position: absolute;
    left: 0;
    top: -2px;
    width: 1px;
    height: 5px;
    background: #86909C;
  }
}

.level-label {
  position: absolute;
  left: 0px;
  /* 向左偏移，使标签显示在线条左侧 */
  top: -22px;
  color: #86909C;
  font-size: 12px;
  //   background: #fff;
  padding: 0 4px;
  border-radius: 2px;

  .level-value {
    color: #507EF7;
  }
}

.scheme-line {
  position: absolute;
  /* left 和 width 现在通过内联样式动态设置 */
  height: 1px;
  border-top: 1px dashed;
  z-index: 11;
}

.scheme-label {
  position: absolute;
  left: 0px;
  /* 向左偏移，使标签显示在线条左侧 */
  top: -22px;
  font-size: 12px;
  padding: 0 4px;
  border-radius: 2px;
}

/* 垂直指示线样式 */
.indicator-line {
  position: absolute;
  width: 1px;
  background: #86909C;
  z-index: 12;

  &::before,
  &::after {
    content: "";
    position: absolute;
    left: -8px;
    width: 16px;
    height: 1px;
    background: transparent;
    border-top: 1px dashed #86909C;
  }

  &::before {
    top: 0px;
    /* 调整位置，让虚线在箭头图标下方 */
  }

  &::after {
    bottom: 0px;
    /* 调整位置，让虚线在箭头图标上方 */
  }


}

.arrow-img {
  position: absolute;
  width: 8px;
  height: 8px;
  left: -3.5px;
  /* 居中对齐 */
  z-index: 13;

  &.top-arrow {
    top: -2px;
  }

  &.bottom-arrow {
    bottom: -2px;
  }

  &.flip-vertical {
    transform: rotate(180deg);
  }
}

.indicator-label {
  position: absolute;
  font-size: 12px;
  color: #86909C;
  white-space: nowrap;
  z-index: 13;

  .indicator-value {
    color: #507EF7;
  }

  &.left-label {
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
  }

  &.right-label {
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
  }

  &.top-label {
    left: 50%;
    top: -25px;
    transform: translateX(-50%);
  }

  &.bottom-label {
    left: 50%;
    bottom: -25px;
    transform: translateX(-50%);
  }
}

/* 水的示意图样式 */
.water-animation {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 36%;
  height: 100%;
  z-index: 1;
  overflow: hidden;
}

.water-animation-left {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 65%;
  height: 70%;
  z-index: 1;
  overflow: hidden;
}

.water-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  animation: waterFlow 4s ease-in-out infinite;
}

/* 水面流动效果层 */
.water-surface-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2;
}

/* 波浪效果 */
.wave {
  position: absolute;
  top: 0;
  left: -100%;
  width: 200%;
  height: 20px;
  background: linear-gradient(90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.1) 25%,
      rgba(255, 255, 255, 0.2) 50%,
      rgba(255, 255, 255, 0.1) 75%,
      transparent 100%);
  border-radius: 50%;
  animation: waveMove 3s ease-in-out infinite;
}

.wave-1 {
  animation-delay: 0s;
  animation-duration: 3s;
}

.wave-2 {
  animation-delay: 1s;
  animation-duration: 4s;
  opacity: 0.7;
}

.wave-3 {
  animation-delay: 2s;
  animation-duration: 5s;
  opacity: 0.5;
}

/* 水面闪光效果 */
.water-shimmer {
  position: absolute;
  top: 0;
  left: -50%;
  width: 150%;
  height: 30px;
  background: linear-gradient(45deg,
      transparent 0%,
      rgba(135, 206, 250, 0.3) 30%,
      rgba(255, 255, 255, 0.4) 50%,
      rgba(135, 206, 250, 0.3) 70%,
      transparent 100%);
  animation: shimmer 6s ease-in-out infinite;
  border-radius: 50%;
}

@keyframes waterFlow {
  0% {
    transform: translateY(0) scale(1);
  }

  25% {
    transform: translateY(-3px) scale(1.02);
  }

  50% {
    transform: translateY(-5px) scale(1.05);
  }

  75% {
    transform: translateY(-3px) scale(1.02);
  }

  100% {
    transform: translateY(0) scale(1);
  }
}

@keyframes waveMove {
  0% {
    transform: translateX(0) translateY(0);
    opacity: 0;
  }

  10% {
    opacity: 1;
  }

  90% {
    opacity: 1;
  }

  100% {
    transform: translateX(100%) translateY(-10px);
    opacity: 0;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(0) rotate(-5deg);
    opacity: 0;
  }

  20% {
    opacity: 0.8;
  }

  80% {
    opacity: 0.8;
  }

  100% {
    transform: translateX(100%) rotate(5deg);
    opacity: 0;
  }
}

/* 特征图片标签样式 */
.feature-label {
  position: absolute;
  font-size: 12px;
  color: #1D2129;
  background: rgba(255, 255, 255, 1);
  padding: 2px 6px;
  border-radius: 2px;
  z-index: 8;
  left: 580px;
  border: 1px solid #F2F3F5;
}

.drinking-outlets-label {
  bottom: 50%;
  left: 59%;
}

.overflow-weir-label {
  bottom: 65%;
  left: 58%;
}

.sluice-gate-label {
  bottom: 75%;
  left: 60%;
}

// .top-sluice-label {
//   bottom: 95%;
//   left: 550px;
// }</style>